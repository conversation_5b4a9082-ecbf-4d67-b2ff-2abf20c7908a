import { ToolResult, ExecutionContext } from '../types/index.js';
export interface BashToolParams {
    command: string;
    workingDirectory?: string;
    timeout?: number;
}
export declare class BashTool {
    execute(params: BashToolParams, context: ExecutionContext): Promise<ToolResult>;
    private formatOutput;
    getDescription(): string;
    validateParams(params: any): params is BashToolParams;
}
//# sourceMappingURL=bash.d.ts.map