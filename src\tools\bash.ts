import { execa } from 'execa';
import { ToolResult, ExecutionContext } from '../types/index.js';
import { nanoid } from 'nanoid';

export interface BashToolParams {
  command: string;
  workingDirectory?: string;
  timeout?: number;
}

export class BashTool {
  async execute(params: BashToolParams, context: ExecutionContext): Promise<ToolResult> {
    const id = nanoid();
    const startTime = Date.now();

    try {
      const workingDir = params.workingDirectory || context.workingDirectory;
      const timeout = params.timeout || context.timeout;

      // Determine shell based on platform
      const shell = process.platform === 'win32' ? 'powershell.exe' : '/bin/bash';
      const shellArgs = process.platform === 'win32' ? ['-Command'] : ['-c'];

      const result = await execa(shell, [...shellArgs, params.command], {
        cwd: workingDir,
        timeout,
        env: { ...process.env, ...context.environment },
        encoding: 'utf8',
        reject: false, // Don't throw on non-zero exit codes
        all: true // Capture both stdout and stderr
      });

      const output = this.formatOutput(result);
      const success = result.exitCode === 0;

      return {
        id,
        success,
        output,
        error: success ? undefined : `Command failed with exit code ${result.exitCode}`,
        executionTime: Date.now() - startTime
      } as ToolResult;

    } catch (error) {
      return {
        id,
        success: false,
        output: '',
        error: error instanceof Error ? error.message : String(error),
        executionTime: Date.now() - startTime
      };
    }
  }

  private formatOutput(result: any): string {
    const parts: string[] = [];

    if (result.stdout && result.stdout.trim()) {
      parts.push(`STDOUT:\n${result.stdout.trim()}`);
    }

    if (result.stderr && result.stderr.trim()) {
      parts.push(`STDERR:\n${result.stderr.trim()}`);
    }

    if (result.all && result.all.trim() && !result.stdout && !result.stderr) {
      parts.push(result.all.trim());
    }

    if (parts.length === 0) {
      return result.exitCode === 0 ? 'Command executed successfully (no output)' : 'Command failed (no output)';
    }

    return parts.join('\n\n');
  }

  getDescription(): string {
    return `Execute bash commands in an interactive shell environment.
    
Usage:
- Run system commands, scripts, or programs
- File system operations (create, move, copy, delete)
- Install software or packages
- System administration tasks
- Process management

Examples:
- ls -la
- mkdir -p project/src && cd project
- npm install --save express
- git status && git add . && git commit -m 'Update'

Notes:
- Commands run in the specified working directory
- Environment variables are inherited from the current process
- Both stdout and stderr are captured
- Exit codes are preserved for error handling`;
  }

  validateParams(params: any): params is BashToolParams {
    return (
      typeof params === 'object' &&
      typeof params.command === 'string' &&
      params.command.trim().length > 0 &&
      (params.workingDirectory === undefined || typeof params.workingDirectory === 'string') &&
      (params.timeout === undefined || typeof params.timeout === 'number')
    );
  }
}
