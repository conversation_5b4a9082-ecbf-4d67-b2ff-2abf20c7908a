import { z } from 'zod';
export declare const LLMProviderSchema: z.<PERSON><["deepseek", "ollama"]>;
export type LLMProvider = z.infer<typeof LLMProviderSchema>;
export declare const DeepseekModelSchema: z.Zod<PERSON><["deepseek-chat", "deepseek-reasoner"]>;
export type DeepseekModel = z.infer<typeof DeepseekModelSchema>;
export declare const ConfigSchema: z.ZodObject<{
    provider: z.ZodEnum<["deepseek", "ollama"]>;
    apiKey: z.ZodOptional<z.ZodString>;
    model: z.ZodString;
    ollamaUrl: z.ZodDefault<z.ZodString>;
    maxRetries: z.<PERSON>efault<z.ZodNumber>;
    retryDelay: z.<PERSON><PERSON><PERSON><z.ZodNumber>;
    timeout: z.ZodDefault<z.ZodNumber>;
}, "strip", z.<PERSON>odType<PERSON>ny, {
    provider: "deepseek" | "ollama";
    model: string;
    ollamaUrl: string;
    maxRetries: number;
    retryDelay: number;
    timeout: number;
    apiKey?: string | undefined;
}, {
    provider: "deepseek" | "ollama";
    model: string;
    apiKey?: string | undefined;
    ollamaUrl?: string | undefined;
    maxRetries?: number | undefined;
    retryDelay?: number | undefined;
    timeout?: number | undefined;
}>;
export type Config = z.infer<typeof ConfigSchema>;
export declare const ToolTypeSchema: z.ZodEnum<["bash", "grep", "glob", "write", "edit", "web"]>;
export type ToolType = z.infer<typeof ToolTypeSchema>;
export interface ToolCall {
    id: string;
    type: ToolType;
    parameters: Record<string, any>;
}
export interface ToolResult {
    id: string;
    success: boolean;
    output: string;
    error?: string | undefined;
    executionTime: number;
}
export interface Message {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: string;
    toolCalls?: ToolCall[] | undefined;
    toolResults?: ToolResult[] | undefined;
    timestamp: Date;
}
export interface LLMResponse {
    content: string;
    toolCalls?: ToolCall[];
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}
export declare class ArienError extends Error {
    code: string;
    retryable: boolean;
    constructor(message: string, code: string, retryable?: boolean);
}
export interface AnimationFrame {
    frame: string;
    duration: number;
}
export interface ExecutionContext {
    workingDirectory: string;
    environment: Record<string, string>;
    timeout: number;
}
export interface RetryOptions {
    maxAttempts: number;
    baseDelay: number;
    maxDelay: number;
    exponentialBase: number;
    jitter: boolean;
}
export interface RetryState {
    attempt: number;
    lastError?: Error;
    totalElapsed: number;
}
//# sourceMappingURL=index.d.ts.map