import { Config, Message } from '../types/index.js';
import { UserInterface } from '../ui/interface.js';
export declare class ArienAgent {
    private llmProvider;
    private toolExecutor;
    private retryManager;
    private ui;
    private messages;
    private context;
    constructor(config: Config, ui: UserInterface);
    processUserInput(input: string): Promise<void>;
    private processConversation;
    private executeTools;
    private formatToolResults;
    getConversationHistory(): Message[];
    clearConversation(): void;
    setWorkingDirectory(directory: string): void;
    getWorkingDirectory(): string;
    validateConnection(): Promise<boolean>;
    getToolDescriptions(): Record<string, string>;
}
//# sourceMappingURL=agent.d.ts.map