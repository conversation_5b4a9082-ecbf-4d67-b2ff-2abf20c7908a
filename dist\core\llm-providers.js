import axios from 'axios';
import { ArienError } from '../types/index.js';
import { RetryManager } from './retry-logic.js';
export class <PERSON><PERSON><PERSON>ider {
    config;
    retryManager;
    constructor(config) {
        this.config = config;
        this.retryManager = new RetryManager();
    }
}
export class DeepseekProvider extends LLMProvider {
    client;
    constructor(config) {
        super(config);
        if (!config.apiKey) {
            throw new ArienError('Deepseek API key is required', 'MISSING_API_KEY');
        }
        this.client = axios.create({
            baseURL: 'https://api.deepseek.com/v1',
            headers: {
                'Authorization': `Bearer ${config.apiKey}`,
                'Content-Type': 'application/json'
            },
            timeout: config.timeout
        });
    }
    async generateResponse(messages) {
        return this.retryManager.executeWithRateLimit(async () => {
            const formattedMessages = this.formatMessages(messages);
            const response = await this.client.post('/chat/completions', {
                model: this.config.model,
                messages: formattedMessages,
                tools: this.getToolDefinitions(),
                tool_choice: 'auto',
                temperature: 0.7,
                max_tokens: 4000,
                stream: false
            });
            return this.parseResponse(response.data);
        });
    }
    async validateConnection() {
        try {
            await this.client.get('/models');
            return true;
        }
        catch (error) {
            return false;
        }
    }
    formatMessages(messages) {
        return messages.map(msg => {
            const formatted = {
                role: msg.role,
                content: msg.content
            };
            if (msg.toolCalls && msg.toolCalls.length > 0) {
                formatted.tool_calls = msg.toolCalls.map(call => ({
                    id: call.id,
                    type: 'function',
                    function: {
                        name: call.type,
                        arguments: JSON.stringify(call.parameters)
                    }
                }));
            }
            if (msg.role === 'tool' && msg.toolResults) {
                formatted.tool_call_id = msg.toolResults[0]?.id;
            }
            return formatted;
        });
    }
    parseResponse(data) {
        const choice = data.choices[0];
        const message = choice.message;
        const response = {
            content: message.content || ''
        };
        if (data.usage) {
            response.usage = {
                promptTokens: data.usage.prompt_tokens || 0,
                completionTokens: data.usage.completion_tokens || 0,
                totalTokens: data.usage.total_tokens || 0
            };
        }
        if (message.tool_calls && message.tool_calls.length > 0) {
            response.toolCalls = message.tool_calls.map((call) => ({
                id: call.id,
                type: call.function.name,
                parameters: JSON.parse(call.function.arguments)
            }));
        }
        return response;
    }
    getToolDefinitions() {
        return [
            {
                type: 'function',
                function: {
                    name: 'bash',
                    description: 'Execute bash commands in an interactive shell',
                    parameters: {
                        type: 'object',
                        properties: {
                            command: {
                                type: 'string',
                                description: 'The bash command to execute'
                            },
                            workingDirectory: {
                                type: 'string',
                                description: 'Optional working directory for the command'
                            }
                        },
                        required: ['command']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'grep',
                    description: 'Search for text patterns within files',
                    parameters: {
                        type: 'object',
                        properties: {
                            pattern: {
                                type: 'string',
                                description: 'Text or regex pattern to search for'
                            },
                            path: {
                                type: 'string',
                                description: 'File or directory path to search in'
                            },
                            recursive: {
                                type: 'boolean',
                                description: 'Search recursively in subdirectories',
                                default: true
                            },
                            ignoreCase: {
                                type: 'boolean',
                                description: 'Case-insensitive search',
                                default: false
                            }
                        },
                        required: ['pattern', 'path']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'glob',
                    description: 'Find files matching specific patterns',
                    parameters: {
                        type: 'object',
                        properties: {
                            pattern: {
                                type: 'string',
                                description: 'Glob pattern to match files'
                            },
                            cwd: {
                                type: 'string',
                                description: 'Working directory for the search'
                            }
                        },
                        required: ['pattern']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'write',
                    description: 'Create or overwrite files with content',
                    parameters: {
                        type: 'object',
                        properties: {
                            path: {
                                type: 'string',
                                description: 'File path to write to'
                            },
                            content: {
                                type: 'string',
                                description: 'Content to write to the file'
                            }
                        },
                        required: ['path', 'content']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'edit',
                    description: 'Edit existing files by replacing text sections',
                    parameters: {
                        type: 'object',
                        properties: {
                            path: {
                                type: 'string',
                                description: 'File path to edit'
                            },
                            search: {
                                type: 'string',
                                description: 'Text to search for and replace'
                            },
                            replace: {
                                type: 'string',
                                description: 'Replacement text'
                            },
                            global: {
                                type: 'boolean',
                                description: 'Replace all occurrences',
                                default: false
                            }
                        },
                        required: ['path', 'search', 'replace']
                    }
                }
            },
            {
                type: 'function',
                function: {
                    name: 'web',
                    description: 'Fetch real-time information from the internet',
                    parameters: {
                        type: 'object',
                        properties: {
                            query: {
                                type: 'string',
                                description: 'Search query or URL to fetch'
                            },
                            maxResults: {
                                type: 'number',
                                description: 'Maximum number of results',
                                default: 5
                            }
                        },
                        required: ['query']
                    }
                }
            }
        ];
    }
}
export class OllamaProvider extends LLMProvider {
    client;
    constructor(config) {
        super(config);
        this.client = axios.create({
            baseURL: config.ollamaUrl,
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: config.timeout
        });
    }
    async generateResponse(messages) {
        return this.retryManager.executeWithNetworkRetry(async () => {
            const formattedMessages = this.formatMessages(messages);
            const response = await this.client.post('/api/chat', {
                model: this.config.model,
                messages: formattedMessages,
                tools: this.getToolDefinitions(),
                stream: false,
                options: {
                    temperature: 0.7,
                    num_predict: 4000
                }
            });
            return this.parseResponse(response.data);
        });
    }
    async validateConnection() {
        try {
            await this.client.get('/api/tags');
            return true;
        }
        catch (error) {
            return false;
        }
    }
    formatMessages(messages) {
        return messages.map(msg => ({
            role: msg.role,
            content: msg.content,
            tool_calls: msg.toolCalls?.map(call => ({
                id: call.id,
                type: 'function',
                function: {
                    name: call.type,
                    arguments: call.parameters
                }
            }))
        }));
    }
    parseResponse(data) {
        const response = {
            content: data.message?.content || ''
        };
        if (data.message?.tool_calls && data.message.tool_calls.length > 0) {
            response.toolCalls = data.message.tool_calls.map((call) => ({
                id: call.id,
                type: call.function.name,
                parameters: call.function.arguments
            }));
        }
        return response;
    }
    getToolDefinitions() {
        // Ollama uses the same tool definitions as Deepseek
        // We need to create a temporary instance to get the definitions
        const tempConfig = { ...this.config, apiKey: 'temp' };
        const tempProvider = new DeepseekProvider(tempConfig);
        return tempProvider.getToolDefinitions();
    }
}
export function createLLMProvider(config) {
    switch (config.provider) {
        case 'deepseek':
            return new DeepseekProvider(config);
        case 'ollama':
            return new OllamaProvider(config);
        default:
            throw new ArienError(`Unsupported LLM provider: ${config.provider}`, 'INVALID_PROVIDER');
    }
}
//# sourceMappingURL=llm-providers.js.map