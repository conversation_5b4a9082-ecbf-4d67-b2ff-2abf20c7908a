import { RetryOptions, RetryState, ArienError } from '../types/index.js';

export class RetryManager {
  private defaultOptions: RetryOptions = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    exponentialBase: 2,
    jitter: true
  };

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: Partial<RetryOptions> = {},
    onRetry?: (state: RetryState) => void
  ): Promise<T> {
    const opts = { ...this.defaultOptions, ...options };
    const state: RetryState = {
      attempt: 0,
      totalElapsed: 0
    };

    const startTime = Date.now();

    while (state.attempt < opts.maxAttempts) {
      state.attempt++;
      
      try {
        const result = await operation();
        return result;
      } catch (error) {
        state.lastError = error as Error;
        state.totalElapsed = Date.now() - startTime;

        // Check if error is retryable
        if (!this.isRetryableError(error as Error)) {
          throw error;
        }

        // If this was the last attempt, throw the error
        if (state.attempt >= opts.maxAttempts) {
          throw new ArienError(
            `Operation failed after ${opts.maxAttempts} attempts: ${state.lastError.message}`,
            'MAX_RETRIES_EXCEEDED',
            false
          );
        }

        // Calculate delay for next attempt
        const delay = this.calculateDelay(state.attempt, opts);
        
        // Notify about retry
        if (onRetry) {
          onRetry(state);
        }

        // Wait before retrying
        await this.sleep(delay);
      }
    }

    // This should never be reached, but TypeScript requires it
    throw state.lastError || new Error('Unknown error occurred');
  }

  private isRetryableError(error: Error): boolean {
    // Network errors
    if (error.message.includes('ECONNRESET') ||
        error.message.includes('ENOTFOUND') ||
        error.message.includes('ETIMEDOUT') ||
        error.message.includes('ECONNREFUSED')) {
      return true;
    }

    // Rate limiting
    if (error.message.includes('rate limit') ||
        error.message.includes('429') ||
        error.message.includes('Too Many Requests')) {
      return true;
    }

    // Server errors (5xx)
    if (error.message.includes('500') ||
        error.message.includes('502') ||
        error.message.includes('503') ||
        error.message.includes('504')) {
      return true;
    }

    // Temporary service unavailable
    if (error.message.includes('Service Unavailable') ||
        error.message.includes('temporarily unavailable')) {
      return true;
    }

    // ArienError with retryable flag
    if (error instanceof ArienError) {
      return error.retryable;
    }

    return false;
  }

  private calculateDelay(attempt: number, options: RetryOptions): number {
    // Exponential backoff: baseDelay * (exponentialBase ^ (attempt - 1))
    let delay = options.baseDelay * Math.pow(options.exponentialBase, attempt - 1);
    
    // Cap at maxDelay
    delay = Math.min(delay, options.maxDelay);
    
    // Add jitter to prevent thundering herd
    if (options.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5);
    }
    
    return Math.floor(delay);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Specialized retry for rate limiting
  async executeWithRateLimit<T>(
    operation: () => Promise<T>,
    onRateLimit?: (retryAfter: number) => void
  ): Promise<T> {
    return this.executeWithRetry(
      operation,
      {
        maxAttempts: 5,
        baseDelay: 2000,
        maxDelay: 60000,
        exponentialBase: 2,
        jitter: true
      },
      (state) => {
        if (state.lastError?.message.includes('rate limit') && onRateLimit) {
          // Extract retry-after header if available
          const retryAfter = this.extractRetryAfter(state.lastError.message);
          onRateLimit(retryAfter);
        }
      }
    );
  }

  private extractRetryAfter(errorMessage: string): number {
    // Try to extract retry-after value from error message
    const match = errorMessage.match(/retry.after[:\s]+(\d+)/i);
    return match && match[1] ? parseInt(match[1]) * 1000 : 5000; // Default to 5 seconds
  }

  // Specialized retry for network operations
  async executeWithNetworkRetry<T>(
    operation: () => Promise<T>,
    onNetworkError?: (attempt: number, error: Error) => void
  ): Promise<T> {
    return this.executeWithRetry(
      operation,
      {
        maxAttempts: 4,
        baseDelay: 1000,
        maxDelay: 10000,
        exponentialBase: 2,
        jitter: true
      },
      (state) => {
        if (onNetworkError && state.lastError) {
          onNetworkError(state.attempt, state.lastError);
        }
      }
    );
  }
}
